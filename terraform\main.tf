# AWS provider region 
provider "aws" {
  region = var.region
  default_tags {
    tags = merge(
      var.global_tags
    )
  }
}


# ACD processor module deployment for each enabled customer
module "acd_processor" {
  for_each = local.acd_enabled_customers
  source   = "./modules/acd-processor"

  name_prefix                   = "${var.environment}-${var.country}-smartanalytics-${each.key}"
  kms_key_name                  = each.value.kms_key_name
  s3_lambda_code_bucket_name    = each.value.s3_lambda_code_bucket_name
  slack_errors_alarm_topic_name = var.slack_errors_alarm_topic_name

  lambda_trigger_queue_name   = each.value.acd.queue_name
  lambda_trigger_dlq_name     = each.value.acd.dlq_name
  subnet_ids                  = each.value.acd.subnet_ids
  vpc_id                      = each.value.acd.vpc_id
  redshift_secret_name        = each.value.acd.redshift_secret_name
  redshift_cluster_identifier = each.value.acd.redshift_cluster_identifier
  environment_variables       = each.value.acd.environment_variables

  tags        = each.value.tags
  project_dir = var.project_dir
}
