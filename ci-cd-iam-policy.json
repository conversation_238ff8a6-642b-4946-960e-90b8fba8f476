{"Version": "2012-10-17", "Statement": [{"Sid": "TerraformStateManagement", "Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:ListBucket", "s3:GetBucketVersioning", "s3:GetBucketLocation", "dynamodb:GetItem", "dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:DescribeTable"], "Resource": ["arn:aws:s3:::terraform-state-*", "arn:aws:s3:::terraform-state-*/*", "arn:aws:dynamodb:*:*:table/terraform-locks"]}, {"Sid": "LambdaManagement", "Effect": "Allow", "Action": ["lambda:CreateFunction", "lambda:UpdateFunctionCode", "lambda:UpdateFunctionConfiguration", "lambda:DeleteFunction", "lambda:GetFunction", "lambda:ListFunctions", "lambda:CreateEventSourceMapping", "lambda:DeleteEventSourceMapping", "lambda:GetEventSourceMapping", "lambda:UpdateEventSourceMapping", "lambda:AddPermission", "lambda:RemovePermission", "lambda:GetPolicy", "lambda:PutProvisionedConcurrencyConfig", "lambda:DeleteProvisionedConcurrencyConfig", "lambda:GetProvisionedConcurrencyConfig"], "Resource": "*"}, {"Sid": "IAMManagement", "Effect": "Allow", "Action": ["iam:CreateRole", "iam:DeleteRole", "iam:GetRole", "iam:PassRole", "iam:AttachRolePolicy", "iam:DetachRolePolicy", "iam:PutRolePolicy", "iam:DeleteRolePolicy", "iam:GetRolePolicy", "iam:ListRolePolicies", "iam:ListAttachedRolePolicies", "iam:CreatePolicy", "iam:DeletePolicy", "iam:GetPolicy", "iam:GetPolicyVersion", "iam:ListPolicyVersions"], "Resource": "*"}, {"Sid": "S3Management", "Effect": "Allow", "Action": ["s3:CreateBucket", "s3:DeleteBucket", "s3:GetBucketLocation", "s3:GetBucketVersioning", "s3:PutBucketVersioning", "s3:GetBucketEncryption", "s3:PutBucketEncryption", "s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:GetObjectTagging", "s3:PutObjectTagging", "s3:GetBucketTagging", "s3:PutBucketTagging"], "Resource": ["arn:aws:s3:::smartanalytics-*", "arn:aws:s3:::smartanalytics-*/*", "arn:aws:s3:::lambda-code-*", "arn:aws:s3:::lambda-code-*/*"]}, {"Sid": "CloudWatchManagement", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:DeleteLogGroup", "logs:DescribeLogGroups", "logs:PutRetentionPolicy", "logs:TagLogGroup", "logs:UntagLogGroup", "cloudwatch:PutMetricAlarm", "cloudwatch:DeleteAlarms", "cloudwatch:DescribeAlarms", "cloudwatch:PutAnomalyDetector", "cloudwatch:DeleteAnomalyDetector", "cloudwatch:DescribeAnomalyDetectors"], "Resource": "*"}, {"Sid": "KMSManagement", "Effect": "Allow", "Action": ["kms:C<PERSON><PERSON><PERSON>", "kms:DescribeKey", "kms:GetKeyPolicy", "kms:PutKeyPolicy", "kms:<PERSON><PERSON><PERSON><PERSON><PERSON>", "kms:DeleteAlias", "kms:ListAliases", "kms:TagResource", "kms:UntagResource"], "Resource": "*"}, {"Sid": "GeneralReadAccess", "Effect": "Allow", "Action": ["sts:GetCallerIdentity", "ec2:DescribeVpcs", "ec2:DescribeSubnets", "ec2:DescribeSecurityGroups", "ec2:DescribeNetworkInterfaces", "sqs:Get<PERSON>ueueAttributes", "sqs:<PERSON><PERSON><PERSON>ues", "sns:GetTopicAttributes", "sns:ListTopics", "redshift:DescribeClusters", "secretsmanager:Describe<PERSON><PERSON><PERSON>", "ssm:GetParameter", "ssm:DescribeParameters"], "Resource": "*"}]}