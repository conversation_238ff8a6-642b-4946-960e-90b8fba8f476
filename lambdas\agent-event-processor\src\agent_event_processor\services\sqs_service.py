"""
SQS message processing service for agent events.

This module handles SQS message parsing, validation, and transformation
into strongly-typed agent event objects.
"""

import json
from typing import List, Dict, Any, Tuple

from aws_lambda_typing.events import SQSRecord
from pydantic import ValidationError

from ..config.logging_config import get_logger
from ..models.events import AgentEvent

logger = get_logger(__name__)


class SQSMessageProcessor:
    """
    Handles SQS message parsing and validation for agent events.

    This class processes SQS records, extracts agent events, and validates
    them against the defined schema with comprehensive error handling.
    """

    def __init__(self):
        """Initialize SQS message processor."""
        logger.info("SQS message processor initialized")

    def parse_sqs_records(
        self, records: List[SQSRecord]
    ) -> Tuple[List[AgentEvent], List[Dict[str, Any]]]:
        """
        Parse SQS records into validated AgentEvent objects.

        Args:
            records: List of SQS record dictionaries from Lambda event.

        Returns:
            Tuple containing:
                - List of successfully parsed and validated Agent<PERSON>vent objects
                - List of failed record information for error handling
        """
        valid_events: List[AgentEvent] = []
        failed_records: List[Dict[str, Any]] = []

        logger.info("Starting SQS record parsing", record_count=len(records))

        for record in records:
            try:
                # Parse the individual record
                event = self._parse_single_record(record)
                valid_events.append(event)

                logger.debug(
                    "Successfully parsed SQS record",
                    message_id=record.get("messageId"),
                    event_type=event.event_type,
                    agent=event.agent,
                    tenant=event.agency_or_element,
                )

            except Exception as e:
                # Log the error and add to failed records
                message_id = record.get("messageId", "unknown")

                logger.error(
                    "Failed to parse SQS record",
                    message_id=message_id,
                    error=str(e),
                    error_type=type(e).__name__,
                    record_body=record.get("body", "")[:500],  # Truncate for logging
                )

                failed_records.append(
                    {
                        "message_id": message_id,
                        "receipt_handle": record.get("receiptHandle"),
                        "error": str(e),
                        "error_type": type(e).__name__,
                    }
                )

        logger.info(
            "SQS record parsing completed",
            total_records=len(records),
            valid_events=len(valid_events),
            failed_records=len(failed_records),
        )

        return valid_events, failed_records

    def _parse_single_record(self, record: SQSRecord) -> AgentEvent:
        """
        Parse a single SQS record into an AgentEvent.

        Args:
            record: Individual SQS record dictionary.

        Returns:
            AgentEvent: Validated agent event object.

        Raises:
            json.JSONDecodeError: If JSON parsing fails.
            ValidationError: If event validation fails.
            KeyError: If required fields are missing.
        """
        try:
            # Extract message body
            body = json.loads(record["body"])

            # Handle SNS wrapped messages (common pattern)
            if "Message" in body:
                # SNS message wrapper
                message_content = json.loads(body["Message"])
                logger.debug(
                    "Unwrapped SNS message",
                    message_id=record.get("messageId"),
                    sns_message_id=body.get("MessageId"),
                )
            else:
                # Direct SQS message
                message_content = body

            # Validate and create event object
            event = AgentEvent.model_validate(message_content)

            # Add SQS metadata for tracking
            event.sqs_message_id = record.get("messageId")
            event.sqs_receipt_handle = record.get("receiptHandle")

            return event

        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(
                f"Invalid JSON in message body: {e.msg}", e.doc, e.pos
            )

        except ValidationError as e:
            # Re-raise with more context
            error_details = []
            for error in e.errors():
                field = " -> ".join(str(loc) for loc in error["loc"])
                error_details.append(f"{field}: {error['msg']}")

            raise ValidationError(
                f"Event validation failed: {'; '.join(error_details)}", model=AgentEvent
            )

        except KeyError as e:
            raise KeyError(f"Missing required field in SQS record: {e}")

    def create_batch_item_failures(
        self, failed_records: List[Dict[str, Any]]
    ) -> List[Dict[str, str]]:
        """
        Create batch item failures for SQS partial batch failure handling.

        Args:
            failed_records: List of failed record information.

        Returns:
            List of batch item failure objects for SQS.
        """
        batch_failures = []

        for failed_record in failed_records:
            if failed_record.get("message_id"):
                batch_failures.append({"itemIdentifier": failed_record["message_id"]})

        logger.info("Created batch item failures", failure_count=len(batch_failures))

        return batch_failures
