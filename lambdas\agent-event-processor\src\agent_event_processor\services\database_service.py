"""
Database service for Redshift connectivity and operations.

This module provides connection management, dimension table operations,
and fact table operations optimized for Lambda execution patterns.
"""

import psycopg2
from contextlib import contextmanager
from typing import Generator, Dict, Any, Optional
from psycopg2.extras import RealDictCursor
from psycopg2.pool import ThreadedConnectionPool

from ..config.logging_config import get_logger
from ..config.settings import DatabaseSettings
from ..models.database import DatabaseCredentials
from ..utils.database_utils import get_db_credentials, get_connection_params

logger = get_logger(__name__)


class RedshiftConnectionManager:
    """
    Manages Redshift database connections with pooling optimized for Lambda.

    This class provides connection pooling, credential management, and
    health checking for reliable database connectivity.
    """

    def __init__(self, settings: DatabaseSettings):
        """
        Initialize connection manager.

        Args:
            settings: Database configuration settings.
        """
        self.settings = settings
        self._pool: Optional[ThreadedConnectionPool] = None
        self._credentials: Optional[DatabaseCredentials] = None

        logger.info(
            "Connection manager initialized",
            max_connections=settings.max_connections,
            connection_timeout=settings.connection_timeout,
        )

    def initialize_pool(self) -> None:
        """
        Initialize connection pool with credentials from Secrets Manager.

        Raises:
            Exception: If pool initialization fails.
        """
        try:
            # Get credentials from Secrets Manager
            self._credentials = get_db_credentials(self.settings.secret_name)

            # Build connection parameters
            connection_params = get_connection_params(self._credentials)

            # Override with Lambda-optimized settings
            connection_params.update(
                {
                    "connect_timeout": self.settings.connection_timeout,
                    "application_name": "agent-event-processor-lambda",
                }
            )

            # Create connection pool (small for Lambda)
            self._pool = ThreadedConnectionPool(
                minconn=1,
                maxconn=min(self.settings.max_connections, 2),  # Lambda limit
                **connection_params,
            )

            logger.info(
                "Database connection pool initialized",
                host=self._credentials.host,
                database=self._credentials.database,
                max_connections=min(self.settings.max_connections, 2),
            )

        except Exception as e:
            logger.error(
                "Failed to initialize connection pool",
                error=str(e),
                secret_name=self.settings.secret_name,
            )
            raise

    @contextmanager
    def get_connection(self) -> Generator[psycopg2.extensions.connection, None, None]:
        """
        Get database connection from pool with automatic cleanup.

        Yields:
            psycopg2.extensions.connection: Database connection with autocommit disabled.

        Raises:
            Exception: If connection cannot be obtained or used.
        """
        if not self._pool:
            self.initialize_pool()

        conn = None
        try:
            # Get connection from pool
            conn = self._pool.getconn()
            conn.autocommit = False

            logger.debug("Database connection acquired from pool")

            yield conn

        except Exception as e:
            # Rollback on any error
            if conn:
                try:
                    conn.rollback()
                    logger.debug("Transaction rolled back due to error")
                except Exception as rollback_error:
                    logger.error(
                        "Failed to rollback transaction", error=str(rollback_error)
                    )
            raise

        finally:
            # Always return connection to pool
            if conn and self._pool:
                try:
                    self._pool.putconn(conn)
                    logger.debug("Database connection returned to pool")
                except Exception as e:
                    logger.error("Failed to return connection to pool", error=str(e))

    def health_check(self) -> bool:
        """
        Check if connection pool is healthy and active.

        Returns:
            bool: True if connection pool is healthy, False otherwise.
        """
        if not self._pool:
            logger.warning("Connection pool not initialized")
            return False

        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()

            is_healthy = result is not None and result[0] == 1

            logger.debug("Connection pool health check completed", healthy=is_healthy)
            return is_healthy

        except Exception as e:
            logger.warning("Connection pool health check failed", error=str(e))
            return False

    def close_pool(self) -> None:
        """Close all connections in the pool."""
        if self._pool:
            try:
                self._pool.closeall()
                logger.info("Connection pool closed")
            except Exception as e:
                logger.error("Failed to close connection pool", error=str(e))
            finally:
                self._pool = None


class DimensionTableManager:
    """
    Manages SCD Type 2 operations for dimension tables.

    This class handles dimension table upserts with proper versioning
    and change tracking for data warehouse operations.
    """

    def __init__(self, connection_manager: RedshiftConnectionManager):
        """
        Initialize dimension table manager.

        Args:
            connection_manager: Database connection manager.
        """
        self.conn_mgr = connection_manager
        logger.info("Dimension table manager initialized")

    def get_or_create_tenant_key(
        self,
        conn: psycopg2.extensions.connection,
        tenant_name: str,
        timezone_name: str = "UTC",
    ) -> int:
        """
        Get or create tenant dimension key.

        Args:
            conn: Database connection.
            tenant_name: Name of the tenant.
            timezone_name: Tenant timezone.

        Returns:
            int: Tenant dimension key.
        """
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # Check if tenant exists
            cursor.execute(
                "SELECT tenant_key FROM dim_tenant WHERE tenant_name = %s",
                (tenant_name,),
            )

            result = cursor.fetchone()
            if result:
                logger.debug(
                    "Found existing tenant",
                    tenant_name=tenant_name,
                    tenant_key=result["tenant_key"],
                )
                return result["tenant_key"]

            # Create new tenant
            cursor.execute(
                """
                INSERT INTO dim_tenant (tenant_name, timezone_name)
                VALUES (%s, %s)
                RETURNING tenant_key
            """,
                (tenant_name, timezone_name),
            )

            tenant_key = cursor.fetchone()["tenant_key"]

            logger.info(
                "Created new tenant",
                tenant_name=tenant_name,
                tenant_key=tenant_key,
                timezone=timezone_name,
            )

            return tenant_key

    def upsert_agent_dimension(
        self,
        conn: psycopg2.extensions.connection,
        agent_data: Dict[str, Any],
        tenant_key: int,
    ) -> int:
        """
        Upsert agent dimension with SCD Type 2 logic.

        Args:
            conn: Database connection.
            agent_data: Agent information from event.
            tenant_key: Foreign key to tenant dimension.

        Returns:
            int: agent_key for the current version.
        """
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # Check for existing current record
            cursor.execute(
                """
                SELECT agent_key, agent_role, agent_uri, workstation
                FROM dim_agent
                WHERE tenant_key = %s
                  AND agent_name = %s
                  AND is_current = true
            """,
                (tenant_key, agent_data["agent_name"]),
            )

            current_record = cursor.fetchone()

            if current_record:
                # Check if attributes changed
                if self._agent_attributes_changed(current_record, agent_data):
                    # Close current record
                    cursor.execute(
                        """
                        UPDATE dim_agent
                        SET is_current = false,
                            valid_to = %s,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE agent_key = %s
                    """,
                        (agent_data["event_timestamp"], current_record["agent_key"]),
                    )

                    # Insert new current record
                    agent_key = self._insert_new_agent_record(
                        cursor, agent_data, tenant_key
                    )

                    logger.info(
                        "Agent dimension updated with new version",
                        agent_name=agent_data["agent_name"],
                        old_agent_key=current_record["agent_key"],
                        new_agent_key=agent_key,
                    )

                    return agent_key

                logger.debug(
                    "Agent attributes unchanged",
                    agent_name=agent_data["agent_name"],
                    agent_key=current_record["agent_key"],
                )
                return current_record["agent_key"]

            # Insert new agent
            agent_key = self._insert_new_agent_record(cursor, agent_data, tenant_key)

            logger.info(
                "New agent dimension created",
                agent_name=agent_data["agent_name"],
                agent_key=agent_key,
            )

            return agent_key

    def _agent_attributes_changed(
        self, current: Dict[str, Any], new_data: Dict[str, Any]
    ) -> bool:
        """
        Check if agent attributes have changed.

        Args:
            current: Current agent record from database.
            new_data: New agent data from event.

        Returns:
            bool: True if attributes have changed.
        """
        return (
            current["agent_role"] != new_data.get("agent_role")
            or current["agent_uri"] != new_data.get("agent_uri")
            or current["workstation"] != new_data.get("workstation")
        )

    def _insert_new_agent_record(
        self, cursor: RealDictCursor, agent_data: Dict[str, Any], tenant_key: int
    ) -> int:
        """
        Insert new agent dimension record.

        Args:
            cursor: Database cursor.
            agent_data: Agent information.
            tenant_key: Foreign key to tenant.

        Returns:
            int: New agent_key.
        """
        cursor.execute(
            """
            INSERT INTO dim_agent (
                agent_name, operator_id, agent_role, agent_uri,
                workstation, tenant_key, valid_from, is_current
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, true)
            RETURNING agent_key
        """,
            (
                agent_data["agent_name"],
                agent_data.get("operator_id"),
                agent_data.get("agent_role"),
                agent_data.get("agent_uri"),
                agent_data.get("workstation"),
                tenant_key,
                agent_data["event_timestamp"],
            ),
        )

        return cursor.fetchone()["agent_key"]
