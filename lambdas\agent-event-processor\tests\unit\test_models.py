"""
Unit tests for data models.

This module tests the Pydantic models for agent events and database
credentials with comprehensive validation scenarios.
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from src.agent_event_processor.models.events import AgentEvent, EventType
from src.agent_event_processor.models.database import DatabaseCredentials, DimensionKeys


class TestAgentEvent:
    """Test suite for AgentEvent model."""
    
    def test_valid_login_event(self, sample_login_event):
        """Test creation of valid login event."""
        event = AgentEvent.model_validate(sample_login_event)
        
        assert event.event_type == EventType.LOGIN
        assert event.agent == "john.doe"
        assert event.agency_or_element == "Brandon911"
        assert event.agent_uri == "tel:+2045553006"
        assert event.reason == "normal"
        assert isinstance(event.timestamp, datetime)
    
    def test_valid_acd_login_event(self, sample_acd_login_event):
        """Test creation of valid ACD login event."""
        event = AgentEvent.model_validate(sample_acd_login_event)
        
        assert event.event_type == EventType.ACD_LOGIN
        assert event.ring_group_name == "911 Queue"
        assert event.ring_group_uri == "sip:<EMAIL>"
    
    def test_timestamp_parsing(self):
        """Test various timestamp formats."""
        # ISO format with Z
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "Login",
            "agencyOrElement": "Test",
            "agent": "test.user"
        }
        event = AgentEvent.model_validate(event_data)
        assert isinstance(event.timestamp, datetime)
        
        # ISO format with timezone
        event_data["timestamp"] = "2024-01-15T10:30:00+00:00"
        event = AgentEvent.model_validate(event_data)
        assert isinstance(event.timestamp, datetime)
    
    def test_invalid_agent_uri(self):
        """Test validation of agent URI format."""
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "Login",
            "agencyOrElement": "Test",
            "agent": "test.user",
            "uri": "invalid-uri"  # Should start with 'tel:'
        }
        
        with pytest.raises(ValidationError) as exc_info:
            AgentEvent.model_validate(event_data)
        
        assert "Agent URI must start with 'tel:'" in str(exc_info.value)
    
    def test_missing_required_fields(self):
        """Test validation with missing required fields."""
        incomplete_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "Login"
            # Missing agencyOrElement and agent
        }
        
        with pytest.raises(ValidationError) as exc_info:
            AgentEvent.model_validate(incomplete_data)
        
        errors = exc_info.value.errors()
        error_fields = [error["loc"][0] for error in errors]
        assert "agency_or_element" in error_fields
        assert "agent" in error_fields
    
    def test_acd_event_validation(self):
        """Test ACD event specific validation."""
        # ACD Login without ring_group_name should fail
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "ACDLogin",
            "agencyOrElement": "Test",
            "agent": "test.user"
            # Missing ring_group_name
        }
        
        with pytest.raises(ValidationError) as exc_info:
            AgentEvent.model_validate(event_data)
        
        assert "ring_group_name is required" in str(exc_info.value)
    
    def test_extra_fields_rejected(self):
        """Test that extra fields are rejected."""
        event_data = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "Login",
            "agencyOrElement": "Test",
            "agent": "test.user",
            "extra_field": "should_be_rejected"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            AgentEvent.model_validate(event_data)
        
        assert "extra_field" in str(exc_info.value)


class TestDatabaseCredentials:
    """Test suite for DatabaseCredentials model."""
    
    def test_valid_credentials(self):
        """Test creation of valid database credentials."""
        creds_data = {
            "host": "test-host.amazonaws.com",
            "port": 5439,
            "database": "test_db",
            "username": "test_user",
            "password": "test_password"
        }
        
        creds = DatabaseCredentials.model_validate(creds_data)
        
        assert creds.host == "test-host.amazonaws.com"
        assert creds.port == 5439
        assert creds.database == "test_db"
        assert creds.username == "test_user"
        assert creds.password == "test_password"
        assert creds.engine == "redshift"  # default value
    
    def test_invalid_port(self):
        """Test validation of port range."""
        creds_data = {
            "host": "test-host.amazonaws.com",
            "port": 70000,  # Invalid port
            "database": "test_db",
            "username": "test_user",
            "password": "test_password"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            DatabaseCredentials.model_validate(creds_data)
        
        assert "Port must be between 1 and 65535" in str(exc_info.value)
    
    def test_empty_host(self):
        """Test validation of empty host."""
        creds_data = {
            "host": "",
            "port": 5439,
            "database": "test_db",
            "username": "test_user",
            "password": "test_password"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            DatabaseCredentials.model_validate(creds_data)
        
        assert "Host cannot be empty" in str(exc_info.value)


class TestDimensionKeys:
    """Test suite for DimensionKeys model."""
    
    def test_valid_dimension_keys(self):
        """Test creation of valid dimension keys."""
        keys_data = {
            "tenant_key": 1,
            "agent_key": 123,
            "date_key": 20240115,
            "time_key": 103000
        }
        
        keys = DimensionKeys.model_validate(keys_data)
        
        assert keys.tenant_key == 1
        assert keys.agent_key == 123
        assert keys.date_key == 20240115
        assert keys.time_key == 103000
        assert keys.queue_key is None  # optional field
    
    def test_invalid_date_key(self):
        """Test validation of date key format."""
        keys_data = {
            "tenant_key": 1,
            "agent_key": 123,
            "date_key": 123,  # Invalid format
            "time_key": 103000
        }
        
        with pytest.raises(ValidationError) as exc_info:
            DimensionKeys.model_validate(keys_data)
        
        assert "Date key must be in YYYYMMDD format" in str(exc_info.value)
    
    def test_invalid_time_key(self):
        """Test validation of time key format."""
        keys_data = {
            "tenant_key": 1,
            "agent_key": 123,
            "date_key": 20240115,
            "time_key": 250000  # Invalid time (25:00:00)
        }
        
        with pytest.raises(ValidationError) as exc_info:
            DimensionKeys.model_validate(keys_data)
        
        assert "Time key must be in HHMMSS format" in str(exc_info.value)
