{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.3", "globals": "30a06fb91b7068c668b0738ff39d3576", "files": {"z_fbf9fece6c68d2c1___init___py": {"hash": "548fa0ab023ab483e133ebfe0deb69d8", "index": {"url": "z_fbf9fece6c68d2c1___init___py.html", "file": "src\\agent_event_processor\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_20164841b8185614___init___py": {"hash": "bde9529ca0d9cd2e75a23975f8e5f9b2", "index": {"url": "z_20164841b8185614___init___py.html", "file": "src\\agent_event_processor\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_20164841b8185614_logging_config_py": {"hash": "76164227019d9cd90b825f8ef3d8fea3", "index": {"url": "z_20164841b8185614_logging_config_py.html", "file": "src\\agent_event_processor\\config\\logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_20164841b8185614_settings_py": {"hash": "9c65e006c93fd7c3dc4b7baa5fda198a", "index": {"url": "z_20164841b8185614_settings_py.html", "file": "src\\agent_event_processor\\config\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 51, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fbf9fece6c68d2c1_lambda_function_py": {"hash": "21ec7a630bc12527b96255943373c61b", "index": {"url": "z_fbf9fece6c68d2c1_lambda_function_py.html", "file": "src\\agent_event_processor\\lambda_function.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c15db3289e0f0aa___init___py": {"hash": "7becc16896b08357cb0e33dfa5009848", "index": {"url": "z_5c15db3289e0f0aa___init___py.html", "file": "src\\agent_event_processor\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c15db3289e0f0aa_database_py": {"hash": "903f42b52cb0dbdcd8bae7466b8f0104", "index": {"url": "z_5c15db3289e0f0aa_database_py.html", "file": "src\\agent_event_processor\\models\\database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c15db3289e0f0aa_events_py": {"hash": "e8683def01dbcb25a10039a0974277eb", "index": {"url": "z_5c15db3289e0f0aa_events_py.html", "file": "src\\agent_event_processor\\models\\events.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56c05ffb73869942___init___py": {"hash": "0818096d148d748dd77e714b6bc19d2d", "index": {"url": "z_56c05ffb73869942___init___py.html", "file": "src\\agent_event_processor\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56c05ffb73869942_database_service_py": {"hash": "4313b163ed3b5039fdff0f5755e7b719", "index": {"url": "z_56c05ffb73869942_database_service_py.html", "file": "src\\agent_event_processor\\services\\database_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 0, "n_missing": 108, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56c05ffb73869942_event_processor_py": {"hash": "6a71139b7b1a309d67c1f10e551c26d8", "index": {"url": "z_56c05ffb73869942_event_processor_py.html", "file": "src\\agent_event_processor\\services\\event_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 114, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_56c05ffb73869942_sqs_service_py": {"hash": "e781b66b4a3629800d4c4bcdd61741d2", "index": {"url": "z_56c05ffb73869942_sqs_service_py.html", "file": "src\\agent_event_processor\\services\\sqs_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 53, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_09c0692e822942fe___init___py": {"hash": "0bb46b941f8ab0c92e6caba169acaaab", "index": {"url": "z_09c0692e822942fe___init___py.html", "file": "src\\agent_event_processor\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_09c0692e822942fe_database_utils_py": {"hash": "5705acb989a1dba0ae07e578cb2c69c0", "index": {"url": "z_09c0692e822942fe_database_utils_py.html", "file": "src\\agent_event_processor\\utils\\database_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 46, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_09c0692e822942fe_metrics_py": {"hash": "6fd224f5bb30bb20d2458d2066fd80ff", "index": {"url": "z_09c0692e822942fe_metrics_py.html", "file": "src\\agent_event_processor\\utils\\metrics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_09c0692e822942fe_timezone_utils_py": {"hash": "321567f995f4d23c3cbb551850e2f4d6", "index": {"url": "z_09c0692e822942fe_timezone_utils_py.html", "file": "src\\agent_event_processor\\utils\\timezone_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}