<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">19%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-14 17:01 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1___init___py.html">src\agent_event_processor\__init__.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614___init___py.html">src\agent_event_processor\config\__init__.py</a></td>
                <td class="name left"><a href="z_20164841b8185614___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html#t17">src\agent_event_processor\config\logging_config.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html#t17"><data value='configure_logging'>configure_logging</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html#t67">src\agent_event_processor\config\logging_config.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html#t67"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html#t80">src\agent_event_processor\config\logging_config.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html#t80"><data value='add_correlation_context'>add_correlation_context</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html">src\agent_event_processor\config\logging_config.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t70">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t70"><data value='validate_log_level'>LoggingSettings.validate_log_level</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t109">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t109"><data value='validate_environment'>Settings.validate_environment</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t124">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t124"><data value='get_settings'>get_settings</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t28">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t28"><data value='get_event_processor'>get_event_processor</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t46">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t46"><data value='get_metrics_collector'>get_metrics_collector</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t66">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html#t66"><data value='lambda_handler'>lambda_handler</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa___init___py.html">src\agent_event_processor\models\__init__.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t30">src\agent_event_processor\models\database.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t30"><data value='validate_port'>DatabaseCredentials.validate_port</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t38">src\agent_event_processor\models\database.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t38"><data value='validate_host'>DatabaseCredentials.validate_host</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t61">src\agent_event_processor\models\database.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t61"><data value='validate_date_key'>DimensionKeys.validate_date_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t69">src\agent_event_processor\models\database.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t69"><data value='validate_time_key'>DimensionKeys.validate_time_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html">src\agent_event_processor\models\database.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t65">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t65"><data value='parse_timestamp'>AgentEvent.parse_timestamp</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t79">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t79"><data value='validate_agent_uri'>AgentEvent.validate_agent_uri</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t87">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t87"><data value='validate_acd_fields'>AgentEvent.validate_acd_fields</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942___init___py.html">src\agent_event_processor\services\__init__.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t30">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t30"><data value='init__'>RedshiftConnectionManager.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t47">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t47"><data value='initialize_pool'>RedshiftConnectionManager.initialize_pool</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t90">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t90"><data value='get_connection'>RedshiftConnectionManager.get_connection</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t138">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t138"><data value='health_check'>RedshiftConnectionManager.health_check</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t167">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t167"><data value='close_pool'>RedshiftConnectionManager.close_pool</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t187">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t187"><data value='init__'>DimensionTableManager.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t197">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t197"><data value='get_or_create_tenant_key'>DimensionTableManager.get_or_create_tenant_key</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t248">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t248"><data value='upsert_agent_dimension'>DimensionTableManager.upsert_agent_dimension</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t319">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t319"><data value='agent_attributes_changed'>DimensionTableManager._agent_attributes_changed</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t340">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t340"><data value='insert_new_agent_record'>DimensionTableManager._insert_new_agent_record</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t33">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t33"><data value='init__'>EventProcessor.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t51">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t51"><data value='process_batch'>EventProcessor.process_batch</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t137">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t137"><data value='process_events'>EventProcessor._process_events</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t207">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t207"><data value='process_single_event'>EventProcessor._process_single_event</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t279">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t279"><data value='process_login_event'>EventProcessor._process_login_event</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t343">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t343"><data value='process_logout_event'>EventProcessor._process_logout_event</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t397">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t397"><data value='process_agent_available_event'>EventProcessor._process_agent_available_event</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t410">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t410"><data value='process_agent_busied_out_event'>EventProcessor._process_agent_busied_out_event</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t423">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t423"><data value='process_acd_login_event'>EventProcessor._process_acd_login_event</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t436">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t436"><data value='process_acd_logout_event'>EventProcessor._process_acd_logout_event</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t28">src\agent_event_processor\services\sqs_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t28"><data value='init__'>SQSMessageProcessor.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t32">src\agent_event_processor\services\sqs_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t32"><data value='parse_sqs_records'>SQSMessageProcessor.parse_sqs_records</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t94">src\agent_event_processor\services\sqs_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t94"><data value='parse_single_record'>SQSMessageProcessor._parse_single_record</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t157">src\agent_event_processor\services\sqs_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t157"><data value='create_batch_item_failures'>SQSMessageProcessor.create_batch_item_failures</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html">src\agent_event_processor\services\sqs_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe___init___py.html">src\agent_event_processor\utils\__init__.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t22">src\agent_event_processor\utils\database_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t22"><data value='get_secrets_manager_client'>get_secrets_manager_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t32">src\agent_event_processor\utils\database_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t32"><data value='get_db_credentials'>get_db_credentials</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t86">src\agent_event_processor\utils\database_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t86"><data value='build_connection_string'>build_connection_string</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t112">src\agent_event_processor\utils\database_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t112"><data value='get_connection_params'>get_connection_params</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t148">src\agent_event_processor\utils\database_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html#t148"><data value='validate_connection_health'>validate_connection_health</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html">src\agent_event_processor\utils\database_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t25">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t25"><data value='init__'>MetricsCollector.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t47">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t47"><data value='put_metric'>MetricsCollector.put_metric</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t93">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t93"><data value='put_business_metric'>MetricsCollector.put_business_metric</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t119">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t119"><data value='put_performance_metric'>MetricsCollector.put_performance_metric</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t152">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t152"><data value='put_database_metric'>MetricsCollector.put_database_metric</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t187">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t187"><data value='flush_metrics'>MetricsCollector.flush_metrics</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t235">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t235"><data value='del__'>MetricsCollector.__del__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t24">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t24"><data value='get_tenant_timezone'>get_tenant_timezone</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t45">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t45"><data value='convert_to_tenant_timezone'>convert_to_tenant_timezone</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t92">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t92"><data value='generate_dimension_keys'>generate_dimension_keys</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t118">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html#t118"><data value='get_shift_date_key'>get_shift_date_key</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>647</td>
                <td>521</td>
                <td>0</td>
                <td class="right" data-ratio="126 647">19%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-14 17:01 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
