variable "name_prefix" {
  type        = string
  description = "String to use as name_prefix for resource names. (dev-us-smartanalytics-customer1)"
}

variable "security_group_ids" {
  type        = list(string)
  description = "List of security group IDs to associate with the Lambda function."
}

variable "subnet_ids" {
  type        = list(string)
  description = "List of subnet IDs to associate with the Lambda function."
}

variable "vpc_id" {
  type        = string
  description = "VPC ID where the Lambda function will be deployed."
}

variable "slack_errors_alarm_topic_name" {
  type        = string
  description = "Name of the SNS topic for data lake CloudWatch alarms."
}

variable "redshift_secret_name" {
  type        = string
  description = "The name of the Redshift secret in AWS Secrets Manager."
}

variable "redshift_cluster_identifier" {
  type        = string
  description = "The identifier of the Redshift cluster."
}

variable "project_dir" {
  description = "Full path to working directory passed from GitLab CI"
  type        = string
}

variable "s3_lambda_code_bucket_name" {
  type        = string
  description = "The name of the S3 bucket containing the Lambda code."
}

variable "kms_key_name" {
  type        = string
  description = "The name of the KMS key to use for encryption."
}

variable "lambda_trigger_queue_name" {
  type        = string
  description = "Lambda trigger to use for deployment."
}

variable "lambda_trigger_dlq_name" {
  type        = string
  description = "Lambda trigger to use for deployment."
}

variable "lambda_memory_size" {
  type        = number
  description = "The amount of memory to allocate to the Lambda function."
  default     = 256
  validation {
    condition     = contains([128, 256, 512, 1024, 2048], var.lambda_memory_size)
    error_message = "lambda_memory_size must be one of 128, 256, 512, 1024, 2048"
  }

}

variable "lambda_timeout" {
  type        = number
  description = "The maximum amount of time the Lambda function can run before it is automatically terminated."
  default     = 60
  validation {
    condition     = var.lambda_timeout >= 1 && var.lambda_timeout <= 900
    error_message = "lambda_timeout must be between 1 and 900 seconds."
  }
}

variable "environment_variables" {
  type        = map(string)
  description = "Environment variables for the Lambda function."
  default     = {}
}

variable "sqs_batch_size" {
  type        = number
  description = "The maximum number of records to retrieve from SQS in a single batch."
  default     = 10
  validation {
    condition     = var.sqs_batch_size >= 1 && var.sqs_batch_size <= 10000
    error_message = "sqs_batch_size must be between 1 and 10000."
  }
}

variable "sqs_max_batching_window_seconds" {
  type        = number
  description = "The maximum amount of time to gather records before invoking the function."
  default     = 0
  validation {
    condition     = var.sqs_max_batching_window_seconds >= 0 && var.sqs_max_batching_window_seconds <= 300
    error_message = "sqs_max_batching_window_seconds must be between 0 and 300."
  }
}

variable "lambda_reserved_concurrency" {
  type        = number
  description = "The amount of reserved concurrent executions for this Lambda function. Set to -1 for unreserved concurrency."
  default     = -1
  validation {
    condition     = var.lambda_reserved_concurrency >= -1
    error_message = "lambda_reserved_concurrency must be -1 (unreserved) or a positive number."
  }
}

variable "lambda_max_concurrency" {
  type        = number
  description = "Maximum number of concurrent executions for the event source mapping."
  default     = 10
  validation {
    condition     = var.lambda_max_concurrency >= 10 && var.lambda_max_concurrency <= 1000
    error_message = "lambda_max_concurrency must be between 10 and 1000."
  }
}

variable "tags" {
  type        = map(string)
  description = "List of key/value pairs for tags."
}
