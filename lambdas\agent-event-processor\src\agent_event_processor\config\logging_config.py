"""
Structured logging configuration for the Agent Event Processor Lambda function.

This module configures structured JSON logging optimized for AWS CloudWatch Logs
with proper correlation tracking and contextual information.
"""

import logging
import sys
from typing import Any, Dict

import structlog

from .settings import get_settings


def configure_logging() -> None:
    """
    Configure structured logging for Lambda environment.

    Sets up structlog with JSON formatting for CloudWatch Logs compatibility
    and includes correlation tracking for request tracing.
    """
    settings = get_settings()

    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.logging.level),
    )

    # Disable boto3/botocore debug logging unless explicitly enabled
    if settings.logging.level != "DEBUG":
        logging.getLogger("boto3").setLevel(logging.WARNING)
        logging.getLogger("botocore").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)

    # Configure structlog processors
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]

    # Add JSON or console formatting based on configuration
    if settings.logging.format.lower() == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer())

    # Configure structlog
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str = __name__) -> structlog.stdlib.BoundLogger:
    """
    Get a configured logger instance.

    Args:
        name: Logger name, defaults to current module.

    Returns:
        structlog.stdlib.BoundLogger: Configured logger instance.
    """
    return structlog.get_logger(name)


def add_correlation_context(
    logger: structlog.stdlib.BoundLogger, correlation_id: str, **additional_context: Any
) -> structlog.stdlib.BoundLogger:
    """
    Add correlation ID and additional context to logger.

    Args:
        logger: Base logger instance.
        correlation_id: Unique correlation ID for request tracking.
        **additional_context: Additional context fields to include.

    Returns:
        structlog.stdlib.BoundLogger: Logger with bound context.
    """
    context = {"correlation_id": correlation_id}
    context.update(additional_context)
    return logger.bind(**context)
