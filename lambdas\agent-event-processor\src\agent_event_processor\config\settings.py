"""
Configuration settings for the Agent Event Processor Lambda function.

This module provides centralized configuration management using environment
variables and AWS Secrets Manager integration.
"""

import os
from functools import lru_cache
from typing import Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database connection settings for Redshift."""

    secret_name: str = Field(..., description="AWS Secrets Manager secret name")
    max_connections: int = Field(
        default=2, description="Maximum database connections for Lambda"
    )
    connection_timeout: int = Field(
        default=30, description="Database connection timeout in seconds"
    )
    query_timeout: int = Field(
        default=300, description="Database query timeout in seconds"
    )

    class Config:
        env_prefix = "REDSHIFT_"


class AWSSettings(BaseSettings):
    """AWS service configuration settings."""

    region: str = Field(default="us-east-1", description="AWS region")

    # CloudWatch Metrics - using the namespace from Terraform
    metrics_namespace: str = Field(
        default="SmartAnalytics/AgentEventProcessor",
        description="CloudWatch metrics namespace",
    )

    # SQS Configuration
    sqs_batch_size: int = Field(default=10, description="SQS batch size for processing")
    sqs_visibility_timeout: int = Field(
        default=300, description="SQS message visibility timeout"
    )

    # Lambda Configuration
    lambda_timeout_buffer: int = Field(
        default=30, description="Buffer time before Lambda timeout (seconds)"
    )

    class Config:
        env_prefix = "AWS_"


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""

    level: str = Field(default="INFO", description="Log level")
    format: str = Field(default="json", description="Log format (json or text)")

    @field_validator("level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level is supported."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()

    model_config = {"env_prefix": "LOG_"}


class Settings(BaseSettings):
    """Main application settings."""

    # Environment
    environment: str = Field(default="dev", description="Deployment environment")

    # Feature flags
    enable_metrics: bool = Field(
        default=True, description="Enable CloudWatch metrics publishing"
    )
    enable_detailed_logging: bool = Field(
        default=True, description="Enable detailed debug logging"
    )

    # Processing settings
    max_retry_attempts: int = Field(
        default=3, description="Maximum retry attempts for failed operations"
    )
    retry_backoff_multiplier: float = Field(
        default=2.0, description="Exponential backoff multiplier for retries"
    )

    # Nested settings
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    aws: AWSSettings = Field(default_factory=AWSSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)

    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment is supported."""
        valid_envs = ["dev", "qa", "prod", "local"]
        if v.lower() not in valid_envs:
            raise ValueError(f"Environment must be one of: {valid_envs}")
        return v.lower()

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
    }


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with caching.

    Returns:
        Settings: Cached application settings instance.

    Note:
        Settings are cached to avoid repeated environment variable parsing
        during Lambda container reuse.
    """
    return Settings()
