"""
Unit tests for SQS service.

This module tests SQS message parsing, validation, and error handling
with comprehensive edge cases and failure scenarios.
"""

import json
import pytest
from unittest.mock import Mock

from src.agent_event_processor.services.sqs_service import SQSMessageProcessor
from src.agent_event_processor.models.events import Agent<PERSON>vent, EventType


class TestSQSMessageProcessor:
    """Test suite for SQS message processor."""
    
    def test_parse_valid_sqs_records(self, sample_login_event):
        """Test parsing of valid SQS records."""
        processor = SQSMessageProcessor()
        
        # Create SQS record with direct message
        sqs_record = {
            "messageId": "test-msg-1",
            "receiptHandle": "test-handle-1",
            "body": json.dumps(sample_login_event)
        }
        
        valid_events, failed_records = processor.parse_sqs_records([sqs_record])
        
        assert len(valid_events) == 1
        assert len(failed_records) == 0
        
        event = valid_events[0]
        assert isinstance(event, AgentEvent)
        assert event.event_type == EventType.LOGIN
        assert event.agent == "john.doe"
        assert event.sqs_message_id == "test-msg-1"
        assert event.sqs_receipt_handle == "test-handle-1"
    
    def test_parse_sns_wrapped_message(self, sample_login_event):
        """Test parsing of SNS-wrapped SQS messages."""
        processor = SQSMessageProcessor()
        
        # Create SNS-wrapped message
        sns_body = {
            "Message": json.dumps(sample_login_event),
            "MessageId": "sns-msg-id"
        }
        
        sqs_record = {
            "messageId": "test-msg-1",
            "receiptHandle": "test-handle-1",
            "body": json.dumps(sns_body)
        }
        
        valid_events, failed_records = processor.parse_sqs_records([sqs_record])
        
        assert len(valid_events) == 1
        assert len(failed_records) == 0
        
        event = valid_events[0]
        assert event.event_type == EventType.LOGIN
        assert event.sqs_message_id == "test-msg-1"
    
    def test_parse_invalid_json(self):
        """Test handling of invalid JSON in message body."""
        processor = SQSMessageProcessor()
        
        sqs_record = {
            "messageId": "test-msg-1",
            "receiptHandle": "test-handle-1",
            "body": "invalid json {"
        }
        
        valid_events, failed_records = processor.parse_sqs_records([sqs_record])
        
        assert len(valid_events) == 0
        assert len(failed_records) == 1
        
        failed_record = failed_records[0]
        assert failed_record["message_id"] == "test-msg-1"
        assert "JSONDecodeError" in failed_record["error_type"]
    
    def test_parse_invalid_event_data(self):
        """Test handling of invalid event data."""
        processor = SQSMessageProcessor()
        
        # Missing required fields
        invalid_event = {
            "timestamp": "2024-01-15T10:30:00Z",
            "eventType": "Login"
            # Missing agencyOrElement and agent
        }
        
        sqs_record = {
            "messageId": "test-msg-1",
            "receiptHandle": "test-handle-1",
            "body": json.dumps(invalid_event)
        }
        
        valid_events, failed_records = processor.parse_sqs_records([sqs_record])
        
        assert len(valid_events) == 0
        assert len(failed_records) == 1
        
        failed_record = failed_records[0]
        assert failed_record["message_id"] == "test-msg-1"
        assert "ValidationError" in failed_record["error_type"]
    
    def test_parse_mixed_valid_invalid_records(self, sample_login_event):
        """Test parsing of mixed valid and invalid records."""
        processor = SQSMessageProcessor()
        
        # Valid record
        valid_record = {
            "messageId": "valid-msg",
            "receiptHandle": "valid-handle",
            "body": json.dumps(sample_login_event)
        }
        
        # Invalid record
        invalid_record = {
            "messageId": "invalid-msg",
            "receiptHandle": "invalid-handle",
            "body": "invalid json"
        }
        
        valid_events, failed_records = processor.parse_sqs_records([
            valid_record, invalid_record
        ])
        
        assert len(valid_events) == 1
        assert len(failed_records) == 1
        
        # Check valid event
        assert valid_events[0].sqs_message_id == "valid-msg"
        
        # Check failed record
        assert failed_records[0]["message_id"] == "invalid-msg"
    
    def test_create_batch_item_failures(self):
        """Test creation of batch item failures for SQS."""
        processor = SQSMessageProcessor()
        
        failed_records = [
            {
                "message_id": "msg-1",
                "receipt_handle": "handle-1",
                "error": "Test error 1"
            },
            {
                "message_id": "msg-2",
                "receipt_handle": "handle-2",
                "error": "Test error 2"
            },
            {
                # Record without message_id should be skipped
                "receipt_handle": "handle-3",
                "error": "Test error 3"
            }
        ]
        
        batch_failures = processor.create_batch_item_failures(failed_records)
        
        assert len(batch_failures) == 2
        assert batch_failures[0]["itemIdentifier"] == "msg-1"
        assert batch_failures[1]["itemIdentifier"] == "msg-2"
    
    def test_empty_records_list(self):
        """Test handling of empty records list."""
        processor = SQSMessageProcessor()
        
        valid_events, failed_records = processor.parse_sqs_records([])
        
        assert len(valid_events) == 0
        assert len(failed_records) == 0
    
    def test_missing_message_id(self, sample_login_event):
        """Test handling of record without message ID."""
        processor = SQSMessageProcessor()
        
        sqs_record = {
            # Missing messageId
            "receiptHandle": "test-handle-1",
            "body": json.dumps(sample_login_event)
        }
        
        valid_events, failed_records = processor.parse_sqs_records([sqs_record])
        
        # Should still process successfully but with unknown message ID
        assert len(valid_events) == 1
        assert valid_events[0].sqs_message_id is None
    
    def test_malformed_sns_message(self):
        """Test handling of malformed SNS message."""
        processor = SQSMessageProcessor()
        
        # SNS wrapper with invalid inner message
        sns_body = {
            "Message": "invalid json {",
            "MessageId": "sns-msg-id"
        }
        
        sqs_record = {
            "messageId": "test-msg-1",
            "receiptHandle": "test-handle-1",
            "body": json.dumps(sns_body)
        }
        
        valid_events, failed_records = processor.parse_sqs_records([sqs_record])
        
        assert len(valid_events) == 0
        assert len(failed_records) == 1
        assert "JSONDecodeError" in failed_records[0]["error_type"]
