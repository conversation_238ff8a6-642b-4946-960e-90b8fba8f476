"""
Event models for agent events with strong typing and validation.

This module defines Pydantic models for all supported agent event types
with comprehensive validation and type safety.
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field, field_validator


class EventType(str, Enum):
    """Supported agent event types."""

    LOGIN = "Login"
    LOGOUT = "Logout"
    AGENT_AVAILABLE = "AgentAvailable"
    AGENT_BUSIED_OUT = "AgentBusiedOut"
    ACD_LOGIN = "ACDLogin"
    ACD_LOGOUT = "ACDLogout"


class AgentEvent(BaseModel):
    """
    Base model for all agent events with comprehensive validation.

    This model handles all agent event types and provides validation
    for required and optional fields based on the event type.
    """

    # Core event fields (required for all events)
    timestamp: datetime = Field(..., description="Event timestamp in UTC")
    event_type: EventType = Field(
        ..., alias="eventType", description="Type of agent event"
    )
    agency_or_element: str = Field(
        ..., alias="agencyOrElement", description="Tenant identifier"
    )
    agent: str = Field(..., description="Agent username/identifier")

    # Common agent fields (optional, present in most events)
    media_label: Optional[str] = Field(None, alias="mediaLabel")
    agent_uri: Optional[str] = Field(None, alias="uri")
    agent_role: Optional[str] = Field(None, alias="agentRole")
    tenant_group: Optional[str] = Field(None, alias="tenantGroup")
    operator_id: Optional[str] = Field(None, alias="operatorId")
    workstation: Optional[str] = Field(None, alias="workstation")
    device_name: Optional[str] = Field(None, alias="deviceName")

    # Event-specific fields
    reason: Optional[str] = None
    response_code: Optional[str] = Field(None, alias="responseCode")
    busied_out_action: Optional[str] = Field(None, alias="busiedOutAction")
    ring_group_name: Optional[str] = Field(None, alias="ringGroupName")
    ring_group_uri: Optional[str] = Field(None, alias="ringGroupUri")

    # Voice quality metrics (optional, for logout events)
    voice_qos: Optional[dict] = Field(None, alias="voiceQOS")

    # SQS metadata (populated during processing)
    sqs_message_id: Optional[str] = None
    sqs_receipt_handle: Optional[str] = None

    @field_validator("timestamp", mode="before")
    @classmethod
    def parse_timestamp(cls, v):
        """Parse timestamp from various formats."""
        if isinstance(v, str):
            # Handle ISO format with timezone
            if v.endswith("Z"):
                v = v.replace("Z", "+00:00")
            return datetime.fromisoformat(v)
        elif isinstance(v, datetime):
            return v
        else:
            raise ValueError(f"Invalid timestamp format: {v}")

    @field_validator("agent_uri")
    @classmethod
    def validate_agent_uri(cls, v):
        """Validate agent URI format."""
        if v is not None and not v.startswith("tel:"):
            raise ValueError("Agent URI must start with 'tel:'")
        return v

    @field_validator("ring_group_name")
    @classmethod
    def validate_acd_fields(cls, v, info):
        """Validate ACD-specific fields are present for ACD events."""
        if hasattr(info, "data") and info.data:
            event_type = info.data.get("event_type") or info.data.get("eventType")
            if event_type in ["ACDLogin", "ACDLogout"]:
                if not v:
                    raise ValueError(
                        f"ring_group_name is required for {event_type} events"
                    )
        return v

    model_config = {
        "populate_by_name": True,
        "use_enum_values": True,
        "validate_assignment": True,
        "extra": "forbid",
    }
