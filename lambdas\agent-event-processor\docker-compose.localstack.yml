# LocalStack setup for local testing of Agent Event Processor
version: '3.8'

services:
  localstack:
    container_name: agent-event-processor-localstack
    image: localstack/localstack:3.0
    ports:
      - "4566:4566"            # LocalStack Gateway
      - "4510-4559:4510-4559"  # External services port range
    environment:
      # LocalStack configuration
      - DEBUG=1
      - SERVICES=lambda,sqs,secretsmanager,cloudwatch,logs
      - DOCKER_HOST=unix:///var/run/docker.sock
      - HOST_TMP_FOLDER=${TMPDIR:-/tmp}/localstack
      - PERSISTENCE=1
      
      # AWS configuration
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      
      # Lambda configuration
      - LAMBDA_EXECUTOR=docker
      - LAMBDA_REMOVE_CONTAINERS=true
      
    volumes:
      - "${TMPDIR:-/tmp}/localstack:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # PostgreSQL for local Redshift simulation
  postgres:
    container_name: agent-event-processor-postgres
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=test_analytics
      - POSTGRES_USER=test_user
      - POSTGRES_PASSWORD=test_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_analytics"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
