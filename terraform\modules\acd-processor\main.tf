terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.0"
    }
  }
  required_version = ">= 1.6"
}

# CloudWatch alarms module for monitoring Lambda function health
module "alarms" {
  source               = "../alarms"
  lambda_function_name = aws_lambda_function.acd_processor.function_name
  sns_topic_name       = var.slack_errors_alarm_topic_name
  evaluation_periods   = 6
  tags                 = var.tags
}
