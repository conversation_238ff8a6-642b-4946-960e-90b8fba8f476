#!/usr/bin/env python3
"""
Test runner script for Agent Event Processor.

This script provides a convenient way to run different types of tests
and quality checks for the Lambda function.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ FAILED: {description}")
        print(f"Exit code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"❌ COMMAND NOT FOUND: {cmd[0]}")
        print(f"Please ensure {cmd[0]} is installed and in your PATH")
        return False


def main():
    """Main test runner."""
    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    print("🚀 Agent Event Processor Test Runner")
    print(f"📁 Working directory: {project_dir}")
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  WARNING: Virtual environment not detected")
        print("   Please activate your virtual environment first:")
        print("   source venv/bin/activate  # Linux/Mac")
        print("   venv\\Scripts\\activate     # Windows")
        print()
    
    # Test commands to run
    tests = [
        # Code formatting
        (["python", "-m", "black", "--check", "src", "tests"], "Code formatting check (Black)"),
        
        # Linting
        (["python", "-m", "flake8", "src", "tests"], "Code linting (Flake8)"),
        
        # Type checking
        (["python", "-m", "mypy", "src"], "Type checking (mypy)"),
        
        # Unit tests
        (["python", "-m", "pytest", "tests/unit", "-v"], "Unit tests"),
        
        # Unit tests with coverage
        (["python", "-m", "pytest", "tests/unit", "--cov=src", "--cov-report=term-missing"], "Unit tests with coverage"),
        
        # Security scan
        (["python", "-m", "bandit", "-r", "src/"], "Security scan (Bandit)"),
    ]
    
    # Run tests
    results = []
    for cmd, description in tests:
        success = run_command(cmd, description)
        results.append((description, success))
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print('='*60)
    
    passed = 0
    failed = 0
    
    for description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {description}")
        if success:
            passed += 1
        else:
            failed += 1
    
    print(f"\nTotal: {len(results)} | Passed: {passed} | Failed: {failed}")
    
    if failed > 0:
        print(f"\n❌ {failed} test(s) failed!")
        sys.exit(1)
    else:
        print(f"\n✅ All tests passed!")
        sys.exit(0)


if __name__ == "__main__":
    main()
