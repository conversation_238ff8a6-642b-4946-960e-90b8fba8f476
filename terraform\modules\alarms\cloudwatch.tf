# CloudWatch alarm for Lambda function errors
resource "aws_cloudwatch_metric_alarm" "lambda_processors_errors" {
  alarm_name          = "${var.lambda_function_name}-errors"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.evaluation_periods
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = 3600 # 1 hour
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "This metric monitors errors in the Lambda function ${var.lambda_function_name}."
  alarm_actions       = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]

  dimensions = {
    FunctionName = var.lambda_function_name
  }

  tags = merge(
    var.tags,
    {
      Name      = "${var.lambda_function_name}-errors"
      AlarmType = "LambdaErrors"
  })
}

# CloudWatch alarm for anomaly detection on Lambda invocations
resource "aws_cloudwatch_metric_alarm" "lambda_processors_invocations" {
  alarm_name          = "${var.lambda_function_name}-invocation-decrease"
  comparison_operator = "LessThanLowerThreshold"
  evaluation_periods  = var.evaluation_periods
  treat_missing_data  = "missing"
  datapoints_to_alarm = var.evaluation_periods
  alarm_description   = "This metric monitors decrease in invocations in the Lambda function ${var.lambda_function_name}."
  alarm_actions       = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]
  threshold_metric_id = "ad1"

  metric_query {
    id          = "m1"
    return_data = true

    metric {
      namespace   = "AWS/Lambda"
      metric_name = "Invocations"
      period      = 3600
      stat        = "Average"

      dimensions = {
        FunctionName = var.lambda_function_name
      }
    }
  }

  metric_query {
    id          = "ad1"
    label       = "Invocations (expected)"
    return_data = true
    expression  = "ANOMALY_DETECTION_BAND(m1, 2)"
  }

  tags = merge(
    var.tags,
    {
      Name      = "${var.lambda_function_name}-invocation-decrease"
      AlarmType = "LambdaInvocationDecrease"
    }
  )
}

# CloudWatch alarm for Lambda function duration warnings
resource "aws_cloudwatch_metric_alarm" "lambda_processors_duration_warn_alarm" {
  alarm_name          = "${var.lambda_function_name}-duration-warn"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = var.evaluation_periods
  metric_name         = "Duration"
  namespace           = "AWS/Lambda"
  period              = 3600 # 1 hour
  statistic           = "Average"
  threshold           = 10000 # 10 seconds
  alarm_description   = "This metric monitors duration in the Lambda function ${var.lambda_function_name}."
  alarm_actions       = ["arn:${data.aws_partition.current.partition}:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}"]

  dimensions = {
    FunctionName = var.lambda_function_name
  }

  tags = merge(
    var.tags,
    {
      Name      = "${var.lambda_function_name}-duration-warn"
      AlarmType = "LambdaDurationWarn"
  })
}
