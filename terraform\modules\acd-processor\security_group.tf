# Security group for Lambda function to access Redshift within VPC, and SQS, SSM, cloudwatch services
resource "aws_security_group" "acd_processor_security_group" {
  name        = "${var.name_prefix}-acd-processor-sg"
  description = "Security group for ACD processor Lambda function"
  vpc_id      = var.vpc_id

  ingress {
    description = "Allow Redshift access"
    from_port   = 5439
    to_port     = 5439
    protocol    = "tcp"
    cidr_blocks = [data.aws_vpc.selected.cidr_block]
  }

  ingress {
    description = "Allow HTTPS access within VPC"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [data.aws_vpc.selected.cidr_block]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.name_prefix}-acd-processor-sg"
    }
  )
}
