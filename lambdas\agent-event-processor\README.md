# Agent Event Processor Lambda

A production-ready AWS Lambda function for processing agent events from SQS queues and populating Amazon Redshift dimension tables with comprehensive error handling, monitoring, and testing.

## Features

- **Event Processing**: Handles Login, Logout, AgentAvailable, AgentBusiedOut, ACDLogin, ACDLogout events
- **Star Schema Design**: Implements proper data warehouse patterns with SCD Type 2 dimensions
- **Strong Typing**: Full type safety with Pydantic models and mypy validation
- **Error Handling**: Comprehensive retry logic, DLQ routing, and failure categorization
- **Monitoring**: Structured logging, CloudWatch metrics, and operational dashboards
- **Testing**: 90%+ test coverage with unit, integration, and performance tests
- **Local Development**: LocalStack integration for local testing and development

## Architecture

```
SQS Queue → Lambda Function → Redshift Data Warehouse
    ↓              ↓                    ↓
   DLQ      CloudWatch Logs      Star Schema Tables
            CloudWatch Metrics   (dims + facts)
```

## Quick Start

### Prerequisites

- Python 3.12+
- Docker and Docker Compose (for local testing)
- AWS CLI configured (for deployment)

### Local Development Setup

1. **Clone and setup virtual environment:**
   ```bash
   cd lambdas/agent-event-processor
   python3.12 -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate     # Windows
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements-dev.txt
   pip install -r requirements.txt
   ```

3. **Setup pre-commit hooks:**
   ```bash
   pre-commit install
   ```

4. **Start LocalStack environment:**
   ```bash
   docker-compose -f docker-compose.localstack.yml up -d
   
   # Wait for services to be ready
   chmod +x scripts/setup_localstack.sh
   ./scripts/setup_localstack.sh
   ```

5. **Run tests:**
   ```bash
   # Unit tests
   pytest tests/unit -v
   
   # All tests with coverage
   pytest --cov=src --cov-report=html
   
   # Specific test categories
   pytest -m unit
   pytest -m integration
   ```

### Code Quality

```bash
# Format code
black src tests

# Lint code
flake8 src tests

# Type checking
mypy src

# Security scan
bandit -r src/

# Run all quality checks
pre-commit run --all-files
```

## Configuration

The Lambda function uses environment variables and AWS Secrets Manager for configuration:

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `REDSHIFT_SECRET_NAME` | Secrets Manager secret name | Required |
| `REDSHIFT_MAX_CONNECTIONS` | Max database connections | 2 |
| `AWS_METRICS_NAMESPACE` | CloudWatch metrics namespace | SmartAnalytics/AgentEventProcessor |
| `LOG_LEVEL` | Logging level | INFO |
| `ENVIRONMENT` | Deployment environment | dev |

### Secrets Manager

Database credentials are stored in AWS Secrets Manager with the following format:

```json
{
  "host": "redshift-cluster.amazonaws.com",
  "port": 5439,
  "database": "analytics",
  "username": "lambda_user",
  "password": "secure_password"
}
```

## Event Types

### Login Event
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "eventType": "Login",
  "agencyOrElement": "Brandon911",
  "agent": "john.doe",
  "mediaLabel": "Audio_1",
  "uri": "tel:+2045553006",
  "agentRole": "Rural - CT",
  "workstation": "OP6",
  "reason": "normal"
}
```

### ACD Login Event
```json
{
  "timestamp": "2024-01-15T10:35:00Z",
  "eventType": "ACDLogin",
  "agencyOrElement": "Brandon911",
  "agent": "john.doe",
  "ringGroupName": "911 Queue",
  "ringGroupUri": "sip:<EMAIL>"
}
```

## Database Schema

### Dimension Tables
- `dim_tenant`: Tenant information with timezone
- `dim_agent`: Agent information (SCD Type 2)
- `dim_queue`: ACD queue information (SCD Type 2)
- `dim_date`: Date dimension
- `dim_time`: Time dimension

### Fact Tables
- `fact_agent_event`: Raw agent events
- `fact_agent_intervals`: Calculated time intervals
- `fact_acd_session`: ACD session tracking

## Testing

### Local Testing with LocalStack

1. **Start LocalStack:**
   ```bash
   docker-compose -f docker-compose.localstack.yml up -d
   ./scripts/setup_localstack.sh
   ```

2. **Send test events:**
   ```bash
   # Set environment for local testing
   export AWS_ENDPOINT_URL=http://localhost:4566
   export REDSHIFT_SECRET_NAME=test-redshift-credentials
   
   # Send test message
   aws --endpoint-url=http://localhost:4566 sqs send-message \
     --queue-url http://localhost:4566/000000000000/agent-events-queue \
     --message-body '{"timestamp":"2024-01-15T10:30:00Z","eventType":"Login","agencyOrElement":"Brandon911","agent":"test.user"}'
   ```

3. **Run Lambda locally:**
   ```bash
   # Install lambda runtime interface emulator
   pip install awslambdaric
   
   # Run function locally
   python -m awslambdaric src.agent_event_processor.lambda_function.lambda_handler
   ```

### Unit Tests

```bash
# Run all unit tests
pytest tests/unit -v

# Run specific test file
pytest tests/unit/test_models.py -v

# Run with coverage
pytest tests/unit --cov=src.agent_event_processor.models
```

### Integration Tests

```bash
# Run integration tests (requires LocalStack)
pytest tests/integration -v

# Run slow tests
pytest -m slow
```

## Monitoring

### CloudWatch Metrics

The function publishes custom metrics to CloudWatch:

- `ProcessedRecords`: Number of records processed
- `SuccessfulRecords`: Number of successfully processed records
- `FailedRecords`: Number of failed records
- `EventsProcessed`: Business metrics by event type and tenant
- `OperationDuration`: Performance metrics by operation
- `DatabaseOperationDuration`: Database operation timing

### Structured Logging

All logs are structured JSON for CloudWatch Logs Insights:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "logger": "agent_event_processor.services.event_processor",
  "message": "Event processed successfully",
  "correlation_id": "abc-123-def",
  "event_type": "Login",
  "agent": "john.doe",
  "tenant": "Brandon911"
}
```

### Alarms

CloudWatch alarms are configured for:
- Lambda function errors
- Duration warnings (>10 seconds)
- Invocation anomaly detection

## Deployment

The Lambda function is deployed using Terraform with the existing infrastructure:

```bash
# Build deployment package
./scripts/build_lambda.sh

# Deploy with Terraform
cd ../../terraform
terragrunt apply
```

## Performance Optimization

- **Connection Pooling**: Reuses database connections across invocations
- **Cold Start Mitigation**: Pre-warms connections and caches settings
- **Batch Processing**: Optimized SQS batch handling
- **Memory Tuning**: Configured for optimal cost/performance ratio

## Security

- **VPC Deployment**: Lambda runs in VPC for secure Redshift access
- **Secrets Manager**: Database credentials stored securely
- **IAM Least Privilege**: Minimal required permissions
- **Encryption**: All data encrypted at rest and in transit

## Contributing

1. Follow the established code style (Black, Flake8, mypy)
2. Write comprehensive tests for new features
3. Update documentation for any changes
4. Ensure all quality checks pass before submitting

## Troubleshooting

### Common Issues

1. **Connection timeouts**: Check VPC configuration and security groups
2. **Permission errors**: Verify IAM roles and Secrets Manager access
3. **Memory errors**: Adjust Lambda memory allocation
4. **Test failures**: Ensure LocalStack is running and properly configured

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
```

### LocalStack Issues

Reset LocalStack state:
```bash
docker-compose -f docker-compose.localstack.yml down -v
docker-compose -f docker-compose.localstack.yml up -d
./scripts/setup_localstack.sh
```
