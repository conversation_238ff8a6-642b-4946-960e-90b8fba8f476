<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">19%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-14 17:01 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1___init___py.html">src\agent_event_processor\__init__.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614___init___py.html">src\agent_event_processor\config\__init__.py</a></td>
                <td class="name left"><a href="z_20164841b8185614___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html">src\agent_event_processor\config\logging_config.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="8 23">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t16">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t16"><data value='DatabaseSettings'>DatabaseSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t30">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t30"><data value='Config'>DatabaseSettings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t34">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t34"><data value='AWSSettings'>AWSSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t58">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t58"><data value='Config'>AWSSettings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t62">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t62"><data value='LoggingSettings'>LoggingSettings</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t80">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html#t80"><data value='Settings'>Settings</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_20164841b8185614_settings_py.html">src\agent_event_processor\config\settings.py</a></td>
                <td class="name left"><a href="z_20164841b8185614_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="42 43">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html">src\agent_event_processor\lambda_function.py</a></td>
                <td class="name left"><a href="z_fbf9fece6c68d2c1_lambda_function_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa___init___py.html">src\agent_event_processor\models\__init__.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t13">src\agent_event_processor\models\database.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t13"><data value='DatabaseCredentials'>DatabaseCredentials</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t45">src\agent_event_processor\models\database.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html#t45"><data value='DimensionKeys'>DimensionKeys</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html">src\agent_event_processor\models\database.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t15">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t15"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t26">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html#t26"><data value='AgentEvent'>AgentEvent</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html">src\agent_event_processor\models\events.py</a></td>
                <td class="name left"><a href="z_5c15db3289e0f0aa_events_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942___init___py.html">src\agent_event_processor\services\__init__.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t22">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t22"><data value='RedshiftConnectionManager'>RedshiftConnectionManager</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t179">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html#t179"><data value='DimensionTableManager'>DimensionTableManager</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html">src\agent_event_processor\services\database_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_database_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t25">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html#t25"><data value='EventProcessor'>EventProcessor</data></a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html">src\agent_event_processor\services\event_processor.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_event_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t20">src\agent_event_processor\services\sqs_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html#t20"><data value='SQSMessageProcessor'>SQSMessageProcessor</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html">src\agent_event_processor\services\sqs_service.py</a></td>
                <td class="name left"><a href="z_56c05ffb73869942_sqs_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe___init___py.html">src\agent_event_processor\utils\__init__.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html">src\agent_event_processor\utils\database_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_database_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t17">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html#t17"><data value='MetricsCollector'>MetricsCollector</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html">src\agent_event_processor\utils\metrics.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_metrics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html">src\agent_event_processor\utils\timezone_utils.py</a></td>
                <td class="name left"><a href="z_09c0692e822942fe_timezone_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>647</td>
                <td>521</td>
                <td>0</td>
                <td class="right" data-ratio="126 647">19%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.3">coverage.py v7.10.3</a>,
            created at 2025-08-14 17:01 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
