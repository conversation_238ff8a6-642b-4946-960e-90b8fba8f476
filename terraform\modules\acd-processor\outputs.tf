# Lambda function outputs
output "lambda_function_name" {
  description = "Name of the Lambda function"
  value       = aws_lambda_function.acd_processor.function_name
}

output "lambda_function_arn" {
  description = "ARN of the Lambda function"
  value       = aws_lambda_function.acd_processor.arn
}

output "lambda_function_invoke_arn" {
  description = "Invoke ARN of the Lambda function"
  value       = aws_lambda_function.acd_processor.invoke_arn
}

output "lambda_role_arn" {
  description = "ARN of the Lambda execution role"
  value       = aws_iam_role.acd_processor_lambda_role.arn
}

output "lambda_role_name" {
  description = "Name of the Lambda execution role"
  value       = aws_iam_role.acd_processor_lambda_role.name
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.acd_processor_log_group.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.acd_processor_log_group.arn
}

output "sqs_event_source_mapping_uuid" {
  description = "UUID of the SQS event source mapping"
  value       = aws_lambda_event_source_mapping.acd_processor_sqs_trigger.uuid
}

output "dlq_event_source_mapping_uuid" {
  description = "UUID of the DLQ event source mapping"
  value       = aws_lambda_event_source_mapping.acd_processor_dlq_trigger.uuid
}

# CloudWatch alarm outputs from the alarms module
output "lambda_errors_alarm_arn" {
  description = "ARN of the Lambda errors alarm"
  value       = module.alarms.lambda_errors_alarm_arn
}

output "lambda_duration_alarm_arn" {
  description = "ARN of the Lambda duration alarm"
  value       = module.alarms.lambda_duration_alarm_arn
}

output "lambda_invocations_alarm_arn" {
  description = "ARN of the Lambda invocations alarm"
  value       = module.alarms.lambda_invocations_alarm_arn
}
