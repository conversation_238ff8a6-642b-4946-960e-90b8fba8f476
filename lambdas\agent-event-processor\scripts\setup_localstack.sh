#!/bin/bash
# Setup script for LocalStack environment
# This script creates the necessary AWS resources for local testing

set -e

# Configuration
LOCALSTACK_ENDPOINT="http://localhost:4566"
AWS_REGION="us-east-1"
QUEUE_NAME="agent-events-queue"
DLQ_NAME="agent-events-dlq"
SECRET_NAME="test-redshift-credentials"

echo "Setting up LocalStack environment for Agent Event Processor..."

# Wait for LocalStack to be ready
echo "Waiting for LocalStack to be ready..."
until curl -s "$LOCALSTACK_ENDPOINT/_localstack/health" | grep -q '"sqs": "available"'; do
    echo "Waiting for LocalStack..."
    sleep 2
done
echo "LocalStack is ready!"

# Configure AWS CLI for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=$AWS_REGION

# Create SQS Dead Letter Queue
echo "Creating SQS Dead Letter Queue..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs create-queue \
    --queue-name $DLQ_NAME \
    --region $AWS_REGION

DLQ_URL=$(aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url \
    --queue-name $DLQ_NAME \
    --region $AWS_REGION \
    --query 'QueueUrl' --output text)

DLQ_ARN=$(aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-attributes \
    --queue-url $DLQ_URL \
    --attribute-names QueueArn \
    --region $AWS_REGION \
    --query 'Attributes.QueueArn' --output text)

echo "Created DLQ: $DLQ_ARN"

# Create main SQS Queue with DLQ configuration
echo "Creating main SQS Queue..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs create-queue \
    --queue-name $QUEUE_NAME \
    --region $AWS_REGION \
    --attributes '{
        "VisibilityTimeoutSeconds": "300",
        "MessageRetentionPeriod": "1209600",
        "RedrivePolicy": "{\"deadLetterTargetArn\":\"'$DLQ_ARN'\",\"maxReceiveCount\":3}"
    }'

QUEUE_URL=$(aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url \
    --queue-name $QUEUE_NAME \
    --region $AWS_REGION \
    --query 'QueueUrl' --output text)

echo "Created main queue: $QUEUE_URL"

# Create Secrets Manager secret for database credentials
echo "Creating Secrets Manager secret..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT secretsmanager create-secret \
    --name $SECRET_NAME \
    --description "Test Redshift credentials for local development" \
    --secret-string '{
        "host": "localhost",
        "port": 5432,
        "database": "test_analytics",
        "username": "test_user",
        "password": "test_password",
        "engine": "postgresql"
    }' \
    --region $AWS_REGION

echo "Created secret: $SECRET_NAME"

# Create CloudWatch Log Group
echo "Creating CloudWatch Log Group..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT logs create-log-group \
    --log-group-name "/aws/lambda/agent-event-processor" \
    --region $AWS_REGION

echo "Created log group: /aws/lambda/agent-event-processor"

echo ""
echo "LocalStack setup complete!"
echo ""
echo "Resources created:"
echo "  - SQS Queue: $QUEUE_URL"
echo "  - SQS DLQ: $DLQ_URL"
echo "  - Secret: $SECRET_NAME"
echo "  - Log Group: /aws/lambda/agent-event-processor"
echo ""
echo "Environment variables for testing:"
echo "  export AWS_ENDPOINT_URL=$LOCALSTACK_ENDPOINT"
echo "  export REDSHIFT_SECRET_NAME=$SECRET_NAME"
echo "  export AWS_REGION=$AWS_REGION"
echo ""
echo "To send test messages:"
echo "  aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs send-message \\"
echo "    --queue-url $QUEUE_URL \\"
echo "    --message-body '{\"timestamp\":\"2024-01-15T10:30:00Z\",\"eventType\":\"Login\",\"agencyOrElement\":\"Brandon911\",\"agent\":\"test.user\"}'"
