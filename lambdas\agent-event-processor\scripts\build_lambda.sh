#!/bin/bash
# Build script for Lambda deployment package
# This script creates a deployment-ready ZIP file for the Lambda function

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_DIR/build"
LAMBDA_DIR="$BUILD_DIR/lambda"
ZIP_FILE="$PROJECT_DIR/../../smart-analytics-acd-processor.zip"

echo "Building Lambda deployment package..."
echo "Project directory: $PROJECT_DIR"
echo "Build directory: $BUILD_DIR"

# Clean previous build
if [ -d "$BUILD_DIR" ]; then
    echo "Cleaning previous build..."
    rm -rf "$BUILD_DIR"
fi

if [ -f "$ZIP_FILE" ]; then
    echo "Removing previous ZIP file..."
    rm -f "$ZIP_FILE"
fi

# Create build directory
mkdir -p "$LAMBDA_DIR"

# Copy source code
echo "Copying source code..."
cp -r "$PROJECT_DIR/src/agent_event_processor" "$LAMBDA_DIR/"

# Create lambda_function.py at root level for Lambda handler
echo "Creating Lambda handler entry point..."
cat > "$LAMBDA_DIR/lambda_function.py" << 'EOF'
"""
Lambda function entry point for Agent Event Processor.

This module provides the main entry point for the AWS Lambda function
and imports the actual handler from the package.
"""

from agent_event_processor.lambda_function import lambda_handler

# Export the handler for Lambda runtime
__all__ = ["lambda_handler"]
EOF

# Install dependencies
echo "Installing production dependencies..."
cd "$PROJECT_DIR"

# Create a temporary requirements file without dev dependencies
pip install -r requirements.txt -t "$LAMBDA_DIR" --no-deps

# Install specific dependencies that are needed
pip install \
    boto3==1.34.34 \
    psycopg2-binary==2.9.9 \
    pydantic==2.5.3 \
    aws-lambda-typing==2.17.0 \
    structlog==23.2.0 \
    tenacity==8.2.3 \
    pytz==2023.4 \
    orjson==3.9.10 \
    -t "$LAMBDA_DIR" \
    --no-deps

# Remove unnecessary files to reduce package size
echo "Cleaning up unnecessary files..."
cd "$LAMBDA_DIR"

# Remove test files and development artifacts
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
find . -type d -name "tests" -exec rm -rf {} + 2>/dev/null || true
find . -type f -name "*.pyc" -delete 2>/dev/null || true
find . -type f -name "*.pyo" -delete 2>/dev/null || true
find . -type f -name "*.pyd" -delete 2>/dev/null || true
find . -type f -name ".coverage" -delete 2>/dev/null || true
find . -type f -name "*.so" -delete 2>/dev/null || true

# Remove boto3 and botocore (provided by Lambda runtime)
rm -rf boto3* botocore* 2>/dev/null || true

# Remove documentation and examples
find . -type d -name "docs" -exec rm -rf {} + 2>/dev/null || true
find . -type d -name "examples" -exec rm -rf {} + 2>/dev/null || true
find . -type f -name "*.md" -delete 2>/dev/null || true
find . -type f -name "*.txt" -delete 2>/dev/null || true
find . -type f -name "*.rst" -delete 2>/dev/null || true

# Create ZIP file
echo "Creating deployment package..."
cd "$LAMBDA_DIR"
zip -r "$ZIP_FILE" . -x "*.pyc" "*.pyo" "*/__pycache__/*" "*/tests/*"

# Get package size
PACKAGE_SIZE=$(du -h "$ZIP_FILE" | cut -f1)
echo ""
echo "Lambda deployment package created successfully!"
echo "Package location: $ZIP_FILE"
echo "Package size: $PACKAGE_SIZE"
echo ""

# Verify package contents
echo "Package contents:"
unzip -l "$ZIP_FILE" | head -20
echo "..."
echo "Total files: $(unzip -l "$ZIP_FILE" | tail -1 | awk '{print $2}')"

# Check if package is within Lambda limits
PACKAGE_SIZE_BYTES=$(stat -f%z "$ZIP_FILE" 2>/dev/null || stat -c%s "$ZIP_FILE" 2>/dev/null)
PACKAGE_SIZE_MB=$((PACKAGE_SIZE_BYTES / 1024 / 1024))

echo ""
if [ $PACKAGE_SIZE_MB -gt 50 ]; then
    echo "⚠️  WARNING: Package size ($PACKAGE_SIZE_MB MB) exceeds Lambda limit (50 MB)"
    echo "   Consider removing unnecessary dependencies or using Lambda layers"
else
    echo "✅ Package size ($PACKAGE_SIZE_MB MB) is within Lambda limits"
fi

echo ""
echo "Build complete! Package ready for deployment."
